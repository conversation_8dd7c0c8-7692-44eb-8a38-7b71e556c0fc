{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\BigMap.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\components\\map\\BigMap.vue", "mtime": 1754041134202}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IG1hcENvbmZpZyB9IGZyb20gJ0AvdXRpbHMvbWFwL21hcENvbmZpZycKaW1wb3J0IHsgbWFwVXRpbHMgfSBmcm9tICdAL3V0aWxzL21hcC9tYXBVdGlscycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnQmlnTWFwJywKICBwcm9wczogewogICAgLy8g5Zyw5Zu+5a655ZmoSUQKICAgIG1hcElkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ2JpZ2VtYXAtY29udGFpbmVyJwogICAgfSwKICAgIC8vIOWcsOWbvuWuveW6pgogICAgd2lkdGg6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnMTAwJScKICAgIH0sCiAgICAvLyDlnLDlm77pq5jluqYKICAgIGhlaWdodDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcxMDAlJwogICAgfSwKICAgIC8vIOWIneWni+S4reW/g+eCuQogICAgY2VudGVyOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiAoKSA9PiBbMTIwLjAsIDMwLjBdCiAgICB9LAogICAgLy8g5Yid5aeL57yp5pS+57qn5YirCiAgICB6b29tOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogNwogICAgfSwKICAgIC8vIOaYr+WQpuaYvuekuuaOp+WItuaMiemSrgogICAgc2hvd0NvbnRyb2xzOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IHRydWUKICAgIH0sCiAgICAvLyDlnLDlm77phY3nva7pgInpobkKICAgIG9wdGlvbnM6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbWFwOiBudWxsLAogICAgICBjdXJyZW50TGF5ZXI6ICdzYXRlbGxpdGUnLAogICAgICBzaG93TGF5ZXJQYW5lbDogZmFsc2UsCiAgICAgIGF2YWlsYWJsZUxheWVyczogWwogICAgICAgIHsga2V5OiAnc2F0ZWxsaXRlJywgbmFtZTogJ+WNq+aYn+WbvicgfSwKICAgICAgICB7IGtleTogJ3NlYScsIG5hbWU6ICfmtbflm74nIH0sCiAgICAgICAgeyBrZXk6ICdzdHJlZXQnLCBuYW1lOiAn6KGX6YGT5Zu+JyB9CiAgICAgIF0sCiAgICAgIGlzRnVsbHNjcmVlbjogZmFsc2UKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXRNYXAoKQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIGlmICh0aGlzLm1hcCkgewogICAgICB0aGlzLm1hcC5yZW1vdmUoKQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Yid5aeL5YyW5Zyw5Zu+CiAgICBhc3luYyBpbml0TWFwKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOetieW+hSBiaWdlbWFwIOW6k+WKoOi9veWujOaIkAogICAgICAgIGF3YWl0IHRoaXMud2FpdEZvckJpZ2VtYXAoKQoKICAgICAgICAvLyDorr7nva7lnLDlm77mnI3liqHlmajlnLDlnYAKICAgICAgICBCTS5Db25maWcuSFRUUF9VUkwgPSBtYXBDb25maWcubWFwSG9zdAoKICAgICAgICAvLyDliJvlu7rlnLDlm77lrp7kvosgLSDkvb/nlKjlrpjmlrnmoYjkvovnmoTmlrnlvI8KICAgICAgICB0aGlzLm1hcCA9IEJNLm1hcCh0aGlzLm1hcElkLCBudWxsLCB7CiAgICAgICAgICBjcnM6IEJNLkNSUy5FUFNHNDMyNiwKICAgICAgICAgIGNlbnRlcjogW3RoaXMuY2VudGVyWzBdLCB0aGlzLmNlbnRlclsxXV0sCiAgICAgICAgICB6b29tOiB0aGlzLnpvb20sCiAgICAgICAgICB6b29tQ29udHJvbDogdHJ1ZSwKICAgICAgICAgIG1pblpvb206IDMsCiAgICAgICAgICBtYXhab29tOiAxOCwKICAgICAgICAgIGF0dHJpYnV0aW9uQ29udHJvbDogZmFsc2UsCiAgICAgICAgICAuLi50aGlzLm9wdGlvbnMKICAgICAgICB9KQoKICAgICAgICAvLyDmt7vliqDpu5jorqTlm77lsYIKICAgICAgICB0aGlzLmFkZERlZmF1bHRMYXllcnMoKQoKICAgICAgICAvLyDnu5HlrprlnLDlm77kuovku7YKICAgICAgICB0aGlzLmJpbmRNYXBFdmVudHMoKQoKICAgICAgICAvLyDop6blj5HlnLDlm77liJ3lp4vljJblrozmiJDkuovku7YKICAgICAgICB0aGlzLiRlbWl0KCdtYXAtcmVhZHknLCB0aGlzLm1hcCkKCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Zyw5Zu+5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJGVtaXQoJ21hcC1lcnJvcicsIGVycm9yKQogICAgICB9CiAgICB9LAoKICAgIC8vIOetieW+hSBiaWdlbWFwIOW6k+WKoOi9vQogICAgd2FpdEZvckJpZ2VtYXAoKSB7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7CiAgICAgICAgaWYgKHdpbmRvdy5CTSkgewogICAgICAgICAgcmVzb2x2ZSgpCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIGxldCBhdHRlbXB0cyA9IDAKICAgICAgICBjb25zdCBtYXhBdHRlbXB0cyA9IDUwCiAgICAgICAgY29uc3QgY2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICAgIGF0dGVtcHRzKysKICAgICAgICAgIGlmICh3aW5kb3cuQk0pIHsKICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKQogICAgICAgICAgICByZXNvbHZlKCkKICAgICAgICAgIH0gZWxzZSBpZiAoYXR0ZW1wdHMgPj0gbWF4QXR0ZW1wdHMpIHsKICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKQogICAgICAgICAgICByZWplY3QobmV3IEVycm9yKCdCaWdlbWFwIGxpYnJhcnkgZmFpbGVkIHRvIGxvYWQnKSkKICAgICAgICAgIH0KICAgICAgICB9LCAxMDApCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOa3u+WKoOm7mOiupOWbvuWxggogICAgYWRkRGVmYXVsdExheWVycygpIHsKICAgICAgLy8g5Yib5bu65Y2r5pif5Zu+5bGCIC0g5L2/55So5a6Y5pa55qGI5L6L55qE5pa55byPCiAgICAgIHRoaXMuc2F0ZWxsaXRlTGF5ZXIgPSBCTS50aWxlTGF5ZXIoJ2JpZ2VtYXAuc2F0ZWxsaXRlJykKCiAgICAgIC8vIOWIm+W7uua1t+WbvuWbvuWxggogICAgICB0aGlzLnNlYUxheWVyID0gQk0udGlsZUxheWVyKCdiaWdlbWFwLnNlYU1hcCcpCgogICAgICAvLyDpu5jorqTmt7vliqDljavmmJ/lm77lsYIKICAgICAgdGhpcy5zYXRlbGxpdGVMYXllci5hZGRUbyh0aGlzLm1hcCkKICAgIH0sCgogICAgLy8g57uR5a6a5Zyw5Zu+5LqL5Lu2CiAgICBiaW5kTWFwRXZlbnRzKCkgewogICAgICB0aGlzLm1hcC5vbignY2xpY2snLCAoZSkgPT4gewogICAgICAgIHRoaXMuJGVtaXQoJ21hcC1jbGljaycsIGUpCiAgICAgIH0pCiAgICAgIAogICAgICB0aGlzLm1hcC5vbignem9vbScsIChlKSA9PiB7CiAgICAgICAgdGhpcy4kZW1pdCgnbWFwLXpvb20nLCBlKQogICAgICB9KQogICAgICAKICAgICAgdGhpcy5tYXAub24oJ21vdmVlbmQnLCAoZSkgPT4gewogICAgICAgIHRoaXMuJGVtaXQoJ21hcC1tb3ZlZW5kJywgZSkKICAgICAgfSkKICAgIH0sCgogICAgLy8g5YiH5o2i5Zu+5bGC6Z2i5p2/5pi+56S6CiAgICB0b2dnbGVMYXllcigpIHsKICAgICAgdGhpcy5zaG93TGF5ZXJQYW5lbCA9ICF0aGlzLnNob3dMYXllclBhbmVsCiAgICB9LAoKICAgIC8vIOWIh+aNouWcsOWbvuWbvuWxggogICAgc3dpdGNoTGF5ZXIobGF5ZXJLZXkpIHsKICAgICAgaWYgKHRoaXMuY3VycmVudExheWVyID09PSBsYXllcktleSkgcmV0dXJuCgogICAgICAvLyDnp7vpmaTlvZPliY3lm77lsYIKICAgICAgaWYgKHRoaXMuY3VycmVudExheWVyID09PSAnc2F0ZWxsaXRlJykgewogICAgICAgIHRoaXMubWFwLnJlbW92ZUxheWVyKHRoaXMuc2F0ZWxsaXRlTGF5ZXIpCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50TGF5ZXIgPT09ICdzZWEnKSB7CiAgICAgICAgdGhpcy5tYXAucmVtb3ZlTGF5ZXIodGhpcy5zZWFMYXllcikKICAgICAgfQoKICAgICAgLy8g5re75Yqg5paw5Zu+5bGCCiAgICAgIGlmIChsYXllcktleSA9PT0gJ3NhdGVsbGl0ZScpIHsKICAgICAgICB0aGlzLnNhdGVsbGl0ZUxheWVyLmFkZFRvKHRoaXMubWFwKQogICAgICB9IGVsc2UgaWYgKGxheWVyS2V5ID09PSAnc2VhJykgewogICAgICAgIHRoaXMuc2VhTGF5ZXIuYWRkVG8odGhpcy5tYXApCiAgICAgIH0KCiAgICAgIHRoaXMuY3VycmVudExheWVyID0gbGF5ZXJLZXkKICAgICAgdGhpcy5zaG93TGF5ZXJQYW5lbCA9IGZhbHNlCiAgICAgIHRoaXMuJGVtaXQoJ2xheWVyLWNoYW5nZWQnLCBsYXllcktleSkKICAgIH0sCgogICAgLy8g6YeN572u6KeG5Zu+CiAgICByZXNldFZpZXcoKSB7CiAgICAgIHRoaXMubWFwLnNldFZpZXcoW3RoaXMuY2VudGVyWzBdLCB0aGlzLmNlbnRlclsxXV0sIHRoaXMuem9vbSkKICAgIH0sCgogICAgLy8g5YiH5o2i5YWo5bGPCiAgICB0b2dnbGVGdWxsc2NyZWVuKCkgewogICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9ICF0aGlzLmlzRnVsbHNjcmVlbgogICAgICB0aGlzLiRlbWl0KCdmdWxsc2NyZWVuLXRvZ2dsZScsIHRoaXMuaXNGdWxsc2NyZWVuKQogICAgfSwKCiAgICAvLyDojrflj5blnLDlm77lrp7kvosKICAgIGdldE1hcCgpIHsKICAgICAgcmV0dXJuIHRoaXMubWFwCiAgICB9LAoKICAgIC8vIOiuvue9ruWcsOWbvuS4reW/g+eCuQogICAgc2V0Q2VudGVyKGxuZywgbGF0LCB6b29tKSB7CiAgICAgIGlmICh0aGlzLm1hcCkgewogICAgICAgIHRoaXMubWFwLnNldFZpZXcoW2xuZywgbGF0XSwgem9vbSB8fCB0aGlzLm1hcC5nZXRab29tKCkpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5re75Yqg5qCH6K6wCiAgICBhZGRNYXJrZXIobG5nLCBsYXQsIG9wdGlvbnMgPSB7fSkgewogICAgICBpZiAoIXRoaXMubWFwKSByZXR1cm4gbnVsbAoKICAgICAgY29uc3QgbWFya2VyID0gQk0ubWFya2VyKFtsbmcsIGxhdF0sIG9wdGlvbnMpCiAgICAgIG1hcmtlci5hZGRUbyh0aGlzLm1hcCkKICAgICAgcmV0dXJuIG1hcmtlcgogICAgfSwKCiAgICAvLyDnp7vpmaTmoIforrAKICAgIHJlbW92ZU1hcmtlcihtYXJrZXIpIHsKICAgICAgaWYgKHRoaXMubWFwICYmIG1hcmtlcikgewogICAgICAgIHRoaXMubWFwLnJlbW92ZUxheWVyKG1hcmtlcikKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["BigMap.vue"], "names": [], "mappings": ";AAmCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BigMap.vue", "sourceRoot": "src/components/map", "sourcesContent": ["<template>\n  <div class=\"big-map-container\">\n    <div \n      :id=\"mapId\" \n      class=\"map-wrapper\"\n      :style=\"{ width: width, height: height }\"\n    ></div>\n    \n    <!-- 地图控制按钮 -->\n    <div class=\"map-controls\" v-if=\"showControls\">\n      <div class=\"control-btn\" @click=\"toggleLayer\" title=\"切换图层\">\n        <i class=\"el-icon-picture\"></i>\n      </div>\n      <div class=\"control-btn\" @click=\"resetView\" title=\"重置视图\">\n        <i class=\"el-icon-refresh\"></i>\n      </div>\n      <div class=\"control-btn\" @click=\"toggleFullscreen\" title=\"全屏\">\n        <i class=\"el-icon-full-screen\"></i>\n      </div>\n    </div>\n\n    <!-- 图层切换面板 -->\n    <div class=\"layer-panel\" v-show=\"showLayerPanel\">\n      <div class=\"layer-item\" \n           v-for=\"layer in availableLayers\" \n           :key=\"layer.key\"\n           :class=\"{ active: currentLayer === layer.key }\"\n           @click=\"switchLayer(layer.key)\">\n        {{ layer.name }}\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapConfig } from '@/utils/map/mapConfig'\nimport { mapUtils } from '@/utils/map/mapUtils'\n\nexport default {\n  name: 'BigMap',\n  props: {\n    // 地图容器ID\n    mapId: {\n      type: String,\n      default: 'bigemap-container'\n    },\n    // 地图宽度\n    width: {\n      type: String,\n      default: '100%'\n    },\n    // 地图高度\n    height: {\n      type: String,\n      default: '100%'\n    },\n    // 初始中心点\n    center: {\n      type: Array,\n      default: () => [120.0, 30.0]\n    },\n    // 初始缩放级别\n    zoom: {\n      type: Number,\n      default: 7\n    },\n    // 是否显示控制按钮\n    showControls: {\n      type: Boolean,\n      default: true\n    },\n    // 地图配置选项\n    options: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      map: null,\n      currentLayer: 'satellite',\n      showLayerPanel: false,\n      availableLayers: [\n        { key: 'satellite', name: '卫星图' },\n        { key: 'sea', name: '海图' },\n        { key: 'street', name: '街道图' }\n      ],\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.initMap()\n  },\n  beforeDestroy() {\n    if (this.map) {\n      this.map.remove()\n    }\n  },\n  methods: {\n    // 初始化地图\n    async initMap() {\n      try {\n        // 等待 bigemap 库加载完成\n        await this.waitForBigemap()\n\n        // 设置地图服务器地址\n        BM.Config.HTTP_URL = mapConfig.mapHost\n\n        // 创建地图实例 - 使用官方案例的方式\n        this.map = BM.map(this.mapId, null, {\n          crs: BM.CRS.EPSG4326,\n          center: [this.center[0], this.center[1]],\n          zoom: this.zoom,\n          zoomControl: true,\n          minZoom: 3,\n          maxZoom: 18,\n          attributionControl: false,\n          ...this.options\n        })\n\n        // 添加默认图层\n        this.addDefaultLayers()\n\n        // 绑定地图事件\n        this.bindMapEvents()\n\n        // 触发地图初始化完成事件\n        this.$emit('map-ready', this.map)\n\n      } catch (error) {\n        console.error('地图初始化失败:', error)\n        this.$emit('map-error', error)\n      }\n    },\n\n    // 等待 bigemap 库加载\n    waitForBigemap() {\n      return new Promise((resolve, reject) => {\n        if (window.BM) {\n          resolve()\n          return\n        }\n\n        let attempts = 0\n        const maxAttempts = 50\n        const checkInterval = setInterval(() => {\n          attempts++\n          if (window.BM) {\n            clearInterval(checkInterval)\n            resolve()\n          } else if (attempts >= maxAttempts) {\n            clearInterval(checkInterval)\n            reject(new Error('Bigemap library failed to load'))\n          }\n        }, 100)\n      })\n    },\n\n    // 添加默认图层\n    addDefaultLayers() {\n      // 创建卫星图层 - 使用官方案例的方式\n      this.satelliteLayer = BM.tileLayer('bigemap.satellite')\n\n      // 创建海图图层\n      this.seaLayer = BM.tileLayer('bigemap.seaMap')\n\n      // 默认添加卫星图层\n      this.satelliteLayer.addTo(this.map)\n    },\n\n    // 绑定地图事件\n    bindMapEvents() {\n      this.map.on('click', (e) => {\n        this.$emit('map-click', e)\n      })\n      \n      this.map.on('zoom', (e) => {\n        this.$emit('map-zoom', e)\n      })\n      \n      this.map.on('moveend', (e) => {\n        this.$emit('map-moveend', e)\n      })\n    },\n\n    // 切换图层面板显示\n    toggleLayer() {\n      this.showLayerPanel = !this.showLayerPanel\n    },\n\n    // 切换地图图层\n    switchLayer(layerKey) {\n      if (this.currentLayer === layerKey) return\n\n      // 移除当前图层\n      if (this.currentLayer === 'satellite') {\n        this.map.removeLayer(this.satelliteLayer)\n      } else if (this.currentLayer === 'sea') {\n        this.map.removeLayer(this.seaLayer)\n      }\n\n      // 添加新图层\n      if (layerKey === 'satellite') {\n        this.satelliteLayer.addTo(this.map)\n      } else if (layerKey === 'sea') {\n        this.seaLayer.addTo(this.map)\n      }\n\n      this.currentLayer = layerKey\n      this.showLayerPanel = false\n      this.$emit('layer-changed', layerKey)\n    },\n\n    // 重置视图\n    resetView() {\n      this.map.setView([this.center[0], this.center[1]], this.zoom)\n    },\n\n    // 切换全屏\n    toggleFullscreen() {\n      this.isFullscreen = !this.isFullscreen\n      this.$emit('fullscreen-toggle', this.isFullscreen)\n    },\n\n    // 获取地图实例\n    getMap() {\n      return this.map\n    },\n\n    // 设置地图中心点\n    setCenter(lng, lat, zoom) {\n      if (this.map) {\n        this.map.setView([lng, lat], zoom || this.map.getZoom())\n      }\n    },\n\n    // 添加标记\n    addMarker(lng, lat, options = {}) {\n      if (!this.map) return null\n\n      const marker = BM.marker([lng, lat], options)\n      marker.addTo(this.map)\n      return marker\n    },\n\n    // 移除标记\n    removeMarker(marker) {\n      if (this.map && marker) {\n        this.map.removeLayer(marker)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.big-map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.map-wrapper {\n  border: 2px solid #0ec6c4;\n  border-radius: 4px;\n}\n\n.map-controls {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.control-btn {\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.control-btn:hover {\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n.layer-panel {\n  position: absolute;\n  top: 10px;\n  right: 50px;\n  z-index: 1000;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 8px;\n  min-width: 100px;\n}\n\n.layer-item {\n  padding: 6px 12px;\n  cursor: pointer;\n  border-radius: 3px;\n  transition: all 0.3s;\n}\n\n.layer-item:hover {\n  background: #f5f5f5;\n}\n\n.layer-item.active {\n  background: #409eff;\n  color: white;\n}\n</style>\n"]}]}