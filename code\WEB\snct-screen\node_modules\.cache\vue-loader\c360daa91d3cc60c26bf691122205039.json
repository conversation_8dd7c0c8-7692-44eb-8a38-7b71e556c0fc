{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue", "mtime": 1754034881177}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/comindexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-04 09:23:59\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-05-07 11:05:02\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\index.vue\r\n-->\r\n<template>\r\n  <div class=\"contents\">\r\n    <div class=\"contetn_left\">\r\n      <div class=\"pagetab\">\r\n        <!-- <div class=\"item\">实时监测</div> -->\r\n        \r\n      </div>\r\n      <ItemWrap class=\"contetn_left-top contetn_lr-item\" title=\"基本信息\">\r\n        \r\n        <div style=\"height: 50px;padding-top: 10px;padding-left: 30px;font-size: 19px;\" >船名：{{ shipName }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">SN：{{ shipSn }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">经度：{{ shipLongitude + shipLongitudeH }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">纬度：{{ shipLatitude + shipLatitudeH }}</div>\r\n        <div style=\"height: 50px;padding-left: 30px;font-size: 19px;\">UTC：{{ shipUtc }}</div>\r\n\r\n      </ItemWrap>\r\n      <ItemWrap class=\"contetn_left-center contetn_lr-item\" title=\"姿态信息\">\r\n      \r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          船首向\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeHeading }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          横摇\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeRolling }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          纵摇\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudePitch}}</div>\r\n        </div>\r\n        \r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          高度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{attitudeHeight}}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          经度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{formatCoordinate(attitudeLongitude)}}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          纬度\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ formatCoordinate(attitudeLatitude) }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          距离\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeDistance }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 13px;font-size: 14px;\">\r\n          船速\r\n          <div style=\"padding-top: 13px;font-size: 20px;\">{{ attitudeSpeed }}</div>\r\n        </div>\r\n\r\n        <div style=\"float: left;width:125px;text-align: center;height: 63px;padding-top: 10px;font-size: 14px;\">\r\n          更新时间\r\n          <div style=\"padding-top: 5px;font-size: 16px;\">{{ attitudeUptime }}</div>\r\n        </div>\r\n\r\n      </ItemWrap>\r\n      <ItemWrap class=\"contetn_left-bottom contetn_lr-item\" title=\"气象信息\">\r\n        <div id=\"main1\" style=\"float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: 15px;float: left;\">\r\n          \r\n        </div>\r\n        <div id=\"main2\" style=\"float: left;width:180px;text-align: center;height: 200px;padding-top: 13px;padding-left: 15px;float: left;\">\r\n\r\n        </div>\r\n        <div style=\"text-align: center;padding-top: 10px;font-size: 14px;\">\r\n          <div style=\"padding-top: 5px;font-size: 16px;\">{{ awsUptime }}</div>\r\n        </div>\r\n      </ItemWrap>\r\n    </div>\r\n    <div class=\"contetn_center\">\r\n      <CenterMap class=\"contetn_center_top\" />\r\n      <!--\r\n      <ItemWrap class=\"contetn_center-bottom\" title=\"安装计划\">\r\n        <CenterBottom />\r\n      </ItemWrap>\r\n      -->\r\n    </div>\r\n    <!--\r\n    <div class=\"contetn_right\">\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警次数\"\r\n      >\r\n        <RightTop />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警排名(TOP8)\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <RightCenter />\r\n      </ItemWrap>\r\n      \r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"数据统计图 \"\r\n      >\r\n        <RightBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\nimport LeftTop from './left-top.vue'\r\nimport LeftCenter from \"./left-center.vue\";\r\nimport LeftBottom from \"./left-bottom.vue\";\r\nimport CenterMap from \"./center-map.vue\";\r\nimport CenterBottom from \"./center-bottom.vue\";\r\nimport RightTop from \"./right-top.vue\";\r\nimport RightCenter from \"./right-center.vue\";\r\nimport RightBottom from \"./right-bottom.vue\";\r\nimport { initWebSocket, dataModule, manualClose, clearData } from '@/utils/webSocket'\r\n\r\nexport default {\r\n  components: {\r\n    LeftTop,\r\n    LeftCenter,\r\n    LeftBottom,\r\n    CenterMap,\r\n    RightTop,\r\n    RightCenter,\r\n    RightBottom,\r\n    CenterBottom,\r\n  },\r\n  data() {\r\n    return {\r\n      shipName: \"N/A\",\r\n      shipMmsi: \"N/A\",\r\n      shipCallSign: \"N/A\",\r\n      shipSn: \"N/A\",\r\n      shipLongitude: \"0\",\r\n      shipLatitude: \"0\",\r\n      shipLongitudeH: \"\",\r\n      shipLatitudeH: \"\",\r\n      shipUtc: \"N/A\",\r\n      attitudeHeading: \"0\",\r\n      attitudeRolling: \"0\",\r\n      attitudePitch: \"0\",\r\n      attitudeHeight: \"0\",\r\n      attitudeLongitude: \"0\",\r\n      attitudeLatitude: \"0\",\r\n      attitudeDistance: \"0\",\r\n      attitudeSpeed: \"0\",  \r\n      attitudeUptime: \"N/A\",\r\n      awsUptime: \"\",\r\n      awsMyChart1: null,\r\n      awsMyChart2: null\r\n    };\r\n  },\r\n  filters: {\r\n    numsFilter(msg) {\r\n      return msg || 0;\r\n    },\r\n  },\r\n  created() {\r\n    this.startDataMonitoring();\r\n  },\r\n\r\n  mounted() {\r\n    //销毁其他页面连接\r\n    manualClose();\r\n\r\n    // 清空数据\r\n    clearData();\r\n\r\n    //与后端建立长连接\r\n    // 检查并恢复企业状态\r\n    this.restoreCompanyState();\r\n    initWebSocket();\r\n\r\n    var chartDom1 = document.getElementById('main1');\r\n    var myChart1 = echarts.init(chartDom1, 'dark');\r\n    var option1;\r\n    var chartDom2 = document.getElementById('main2');\r\n    var myChart2 = echarts.init(chartDom2, 'dark');\r\n    var option2;\r\n    // 气象仪图表数据\r\n    option1 = {\r\n      backgroundColor: \"#03050c\",\r\n      series: [\r\n        {\r\n          min: 0,\r\n          max: 360,\r\n          splitNumber: 4,\r\n          radius: \"100%\",\r\n          //startAngle: 90,\r\n          //endAngle: 90.0000001,\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          progress: {\r\n            show: true\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: '{value}',\r\n            offsetCenter: [0, '80%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 0,\r\n              name: '风向'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    option2 = {\r\n      backgroundColor: \"#03050c\",\r\n      series: [\r\n        {\r\n          min: 0,\r\n          max: 60,\r\n          splitNumber: 4,\r\n          radius: \"100%\",\r\n          //startAngle: 90,\r\n          //endAngle: 90.0000001,\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          progress: {\r\n            show: true\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            formatter: '{value}',\r\n            offsetCenter: [0, '80%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 0,\r\n              name: '风速'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n    option1 && myChart1.setOption(option1);\r\n    option2 && myChart2.setOption(option2);\r\n    this.awsMyChart1 = myChart1;\r\n    this.awsMyChart2 = myChart2;\r\n\r\n  },\r\n  methods: {\r\n    // 转换NMEA格式的经纬度为十进制度数\r\n    convertCoordinate(coordinate, type) {\r\n      if (!coordinate) return '';\r\n\r\n      // 移除最后的方向字母（E/W/N/S）\r\n      const direction = coordinate.slice(-1);\r\n      const numericPart = coordinate.slice(0, -1);\r\n\r\n      let degrees, minutes;\r\n\r\n      if (type === 'longitude') {\r\n        // 经度格式：DDDMM.MMMMMM (前3位是度，后面是分)\r\n        degrees = parseInt(numericPart.substring(0, 3));\r\n        minutes = parseFloat(numericPart.substring(3));\r\n      } else {\r\n        // 纬度格式：DDMM.MMMMMM (前2位是度，后面是分)\r\n        degrees = parseInt(numericPart.substring(0, 2));\r\n        minutes = parseFloat(numericPart.substring(2));\r\n      }\r\n\r\n      // 转换为十进制度数\r\n      let decimal = degrees + minutes / 60;\r\n\r\n      // 根据方向确定正负\r\n      if (direction === 'W' || direction === 'S') {\r\n        decimal = -decimal;\r\n      }\r\n\r\n      // 保留6位小数\r\n      return decimal.toFixed(6);\r\n    },\r\n\r\n    // 格式化经纬度显示（保留6位小数）\r\n    formatCoordinate(coordinate) {\r\n      if (!coordinate) return '';\r\n      const num = parseFloat(coordinate);\r\n      return isNaN(num) ? coordinate : num.toFixed(6);\r\n    },\r\n\r\n    // 恢复企业状态\r\n    restoreCompanyState() {\r\n      let deptId = '101'; // 默认值\r\n      try {\r\n        const sessionCompany = sessionStorage.getItem('selectedCompany');\r\n        if (sessionCompany) {\r\n          const companyInfo = JSON.parse(sessionCompany);\r\n          if (companyInfo.sendtext) {\r\n            dataModule.sendtext = companyInfo.sendtext;\r\n            return;\r\n          }\r\n          if (companyInfo.deptId) {\r\n            deptId = companyInfo.deptId;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.warn('读取sessionStorage中的企业信息失败:', error);\r\n      }\r\n\r\n      dataModule.sendtext = `type66#${deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`;\r\n    },\r\n\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData()\r\n      }, 1000)\r\n    },\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0B01) {\r\n        this.shipName = dataModule.D0B01.name;\r\n        this.shipMmsi = dataModule.D0B01.mmsi;\r\n        this.shipCallSign = dataModule.D0B01.callSign;\r\n        this.shipSn = dataModule.D0B01.sn;\r\n        // 转换经纬度格式\r\n        this.shipLongitude = this.convertCoordinate(dataModule.D0B01.longitude, 'longitude')+\"°\";\r\n        this.shipLatitude = this.convertCoordinate(dataModule.D0B01.latitude, 'latitude')+\"°\";\r\n        this.shipLongitudeH = dataModule.D0B01.longitudeHemisphere;\r\n        this.shipLatitudeH = dataModule.D0B01.latitudeHemisphere;\r\n        this.shipUtc = dataModule.D0B01.utc;\r\n\r\n        this.attitudeHeading = dataModule.D0B02.attitudeHeading;\r\n        this.attitudeRolling = dataModule.D0B02.attitudeRolling;\r\n        this.attitudePitch = dataModule.D0B02.attitudePitch;\r\n        this.attitudeHeight = dataModule.D0B02.attitudeHeight;\r\n        this.attitudeLongitude = dataModule.D0B02.attitudeLongitude;\r\n        this.attitudeLatitude = dataModule.D0B02.attitudeLatitude;\r\n        this.attitudeDistance = dataModule.D0B02.attitudeDistance;\r\n        this.attitudeSpeed = dataModule.D0B02.attitudeSpeed;\r\n        this.attitudeUptime = dataModule.D0B02.attitudeUptime;\r\n        \r\n        //this.awsSpeed = dataModule.D0B03.awsSpeed;\r\n        //this.awsDirection = dataModule.D0B03.awsDirection;\r\n        if(isNaN(dataModule.D0B03.awsSpeed)){\r\n          dataModule.D0B03.awsSpeed = 0;\r\n        }\r\n        if(isNaN(dataModule.D0B03.awsDirection)){\r\n          dataModule.D0B03.awsDirection = 0;\r\n        }\r\n\r\n        this.awsMyChart1.setOption({\r\n          series: [\r\n            {\r\n              data: [\r\n                {\r\n                  value: dataModule.D0B03.awsDirection,\r\n                  name: '风向'\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n\r\n        this.awsMyChart2.setOption({\r\n          series: [\r\n            {\r\n              data: [\r\n                {\r\n                  value: dataModule.D0B03.awsSpeed,\r\n                  name: '风速'\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n\r\n      }\r\n\r\n      if (dataModule.D0B03) {\r\n        this.awsUptime = dataModule.D0B03.awsUptime;\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 内容\r\n.contents {\r\n  .contetn_left,\r\n  .contetn_right {\r\n    width: 430px;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .contetn_left {\r\n    height: 960px;\r\n    gap: 10px;\r\n  }\r\n\r\n  .contetn_center {\r\n    height: 960px;\r\n    width: 1439px;\r\n  }\r\n\r\n  //左右两侧 三个块\r\n  .contetn_lr-item {\r\n    height: 311px;\r\n  }\r\n\r\n  .contetn_center_top {\r\n    width: 100%;\r\n  }\r\n\r\n  // 中间\r\n  .contetn_center {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n  }\r\n\r\n  .contetn_center-bottom {\r\n    height: 315px;\r\n  }\r\n\r\n  //左边 右边 结构一样\r\n  .contetn_left,\r\n  .contetn_right {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n    position: relative;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}