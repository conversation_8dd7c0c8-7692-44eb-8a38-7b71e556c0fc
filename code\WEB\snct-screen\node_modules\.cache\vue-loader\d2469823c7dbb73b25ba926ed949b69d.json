{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\test\\MapTest.vue?vue&type=style&index=0&id=e13177ea&scoped=true&lang=css&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\test\\MapTest.vue", "mtime": 1754041218725}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5tYXAtdGVzdC1jb250YWluZXIgewogIHBhZGRpbmc6IDIwcHg7CiAgaGVpZ2h0OiAxMDB2aDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCi5oZWFkZXIgewogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAxMHB4OwogIGJhY2tncm91bmQ6ICNmNWY1ZjU7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgouaGVhZGVyIGgyIHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5jb250cm9scyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEwcHg7Cn0KCi5tYXAtY29udGFpbmVyIHsKICBmbGV4OiAxOwogIGJvcmRlcjogMnB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5pbmZvLXBhbmVsIHsKICBtYXJnaW4tdG9wOiAyMHB4OwogIHBhZGRpbmc6IDE1cHg7CiAgYmFja2dyb3VuZDogI2Y5ZjlmOTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2RkZDsKfQoKLmluZm8tcGFuZWwgaDMgewogIG1hcmdpbi10b3A6IDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5pbmZvLXBhbmVsIHAgewogIG1hcmdpbjogOHB4IDA7CiAgY29sb3I6ICM2NjY7Cn0K"}, {"version": 3, "sources": ["MapTest.vue"], "names": [], "mappings": ";AA6HA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "MapTest.vue", "sourceRoot": "src/views/test", "sourcesContent": ["<template>\n  <div class=\"map-test-container\">\n    <div class=\"header\">\n      <h2>BigMap组件测试页面</h2>\n      <div class=\"controls\">\n        <el-button @click=\"changeCenter\" type=\"primary\">切换中心点</el-button>\n        <el-button @click=\"addTestMarker\" type=\"success\">添加标记</el-button>\n        <el-button @click=\"clearMarkers\" type=\"warning\">清除标记</el-button>\n      </div>\n    </div>\n    \n    <div class=\"map-container\">\n      <BigMap\n        ref=\"bigMap\"\n        :center=\"mapCenter\"\n        :zoom=\"mapZoom\"\n        :height=\"'600px'\"\n        :show-controls=\"true\"\n        @map-ready=\"onMapReady\"\n        @map-error=\"onMapError\"\n        @map-click=\"onMapClick\"\n        @layer-changed=\"onLayerChanged\"\n      />\n    </div>\n    \n    <div class=\"info-panel\">\n      <h3>地图信息</h3>\n      <p><strong>状态:</strong> {{ mapStatus }}</p>\n      <p><strong>中心点:</strong> {{ mapCenter.join(', ') }}</p>\n      <p><strong>缩放级别:</strong> {{ mapZoom }}</p>\n      <p><strong>点击位置:</strong> {{ clickPosition }}</p>\n      <p><strong>当前图层:</strong> {{ currentLayer }}</p>\n      <p><strong>标记数量:</strong> {{ markers.length }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport BigMap from '@/components/map/BigMap.vue'\n\nexport default {\n  name: 'MapTest',\n  components: {\n    BigMap\n  },\n  data() {\n    return {\n      mapCenter: [120.0, 30.0],\n      mapZoom: 7,\n      mapStatus: '初始化中...',\n      clickPosition: '未点击',\n      currentLayer: 'satellite',\n      markers: [],\n      testCenters: [\n        [120.0, 30.0], // 中国海域\n        [118.1, 24.5], // 厦门\n        [121.5, 31.2], // 上海\n        [113.3, 23.1]  // 广州\n      ],\n      currentCenterIndex: 0\n    }\n  },\n  methods: {\n    onMapReady(map) {\n      this.mapStatus = '地图加载成功'\n      console.log('地图初始化完成:', map)\n    },\n    \n    onMapError(error) {\n      this.mapStatus = '地图加载失败: ' + error.message\n      console.error('地图初始化失败:', error)\n    },\n    \n    onMapClick(event) {\n      if (event.latlng) {\n        this.clickPosition = `${event.latlng.lat.toFixed(4)}, ${event.latlng.lng.toFixed(4)}`\n      }\n      console.log('地图点击事件:', event)\n    },\n    \n    onLayerChanged(layerKey) {\n      this.currentLayer = layerKey\n      console.log('图层切换:', layerKey)\n    },\n    \n    changeCenter() {\n      this.currentCenterIndex = (this.currentCenterIndex + 1) % this.testCenters.length\n      this.mapCenter = [...this.testCenters[this.currentCenterIndex]]\n      \n      // 使用组件方法设置中心点\n      if (this.$refs.bigMap) {\n        this.$refs.bigMap.setCenter(this.mapCenter[0], this.mapCenter[1], this.mapZoom)\n      }\n    },\n    \n    addTestMarker() {\n      if (this.$refs.bigMap) {\n        const randomLng = this.mapCenter[0] + (Math.random() - 0.5) * 2\n        const randomLat = this.mapCenter[1] + (Math.random() - 0.5) * 2\n        \n        const marker = this.$refs.bigMap.addMarker(randomLng, randomLat, {\n          title: `标记 ${this.markers.length + 1}`\n        })\n        \n        if (marker) {\n          this.markers.push(marker)\n          this.$message.success(`添加标记成功，位置: ${randomLng.toFixed(4)}, ${randomLat.toFixed(4)}`)\n        }\n      }\n    },\n    \n    clearMarkers() {\n      if (this.$refs.bigMap) {\n        this.markers.forEach(marker => {\n          this.$refs.bigMap.removeMarker(marker)\n        })\n        this.markers = []\n        this.$message.success('已清除所有标记')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.map-test-container {\n  padding: 20px;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: #f5f5f5;\n  border-radius: 4px;\n}\n\n.header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.controls {\n  display: flex;\n  gap: 10px;\n}\n\n.map-container {\n  flex: 1;\n  border: 2px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.info-panel {\n  margin-top: 20px;\n  padding: 15px;\n  background: #f9f9f9;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\n.info-panel h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.info-panel p {\n  margin: 8px 0;\n  color: #666;\n}\n</style>\n"]}]}