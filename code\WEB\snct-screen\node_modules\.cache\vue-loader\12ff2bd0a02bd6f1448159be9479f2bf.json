{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\test\\MapTest.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\test\\MapTest.vue", "mtime": 1754041218725}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapTest.vue"], "names": [], "mappings": ";AAsCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapTest.vue", "sourceRoot": "src/views/test", "sourcesContent": ["<template>\n  <div class=\"map-test-container\">\n    <div class=\"header\">\n      <h2>BigMap组件测试页面</h2>\n      <div class=\"controls\">\n        <el-button @click=\"changeCenter\" type=\"primary\">切换中心点</el-button>\n        <el-button @click=\"addTestMarker\" type=\"success\">添加标记</el-button>\n        <el-button @click=\"clearMarkers\" type=\"warning\">清除标记</el-button>\n      </div>\n    </div>\n    \n    <div class=\"map-container\">\n      <BigMap\n        ref=\"bigMap\"\n        :center=\"mapCenter\"\n        :zoom=\"mapZoom\"\n        :height=\"'600px'\"\n        :show-controls=\"true\"\n        @map-ready=\"onMapReady\"\n        @map-error=\"onMapError\"\n        @map-click=\"onMapClick\"\n        @layer-changed=\"onLayerChanged\"\n      />\n    </div>\n    \n    <div class=\"info-panel\">\n      <h3>地图信息</h3>\n      <p><strong>状态:</strong> {{ mapStatus }}</p>\n      <p><strong>中心点:</strong> {{ mapCenter.join(', ') }}</p>\n      <p><strong>缩放级别:</strong> {{ mapZoom }}</p>\n      <p><strong>点击位置:</strong> {{ clickPosition }}</p>\n      <p><strong>当前图层:</strong> {{ currentLayer }}</p>\n      <p><strong>标记数量:</strong> {{ markers.length }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport BigMap from '@/components/map/BigMap.vue'\n\nexport default {\n  name: 'MapTest',\n  components: {\n    BigMap\n  },\n  data() {\n    return {\n      mapCenter: [120.0, 30.0],\n      mapZoom: 7,\n      mapStatus: '初始化中...',\n      clickPosition: '未点击',\n      currentLayer: 'satellite',\n      markers: [],\n      testCenters: [\n        [120.0, 30.0], // 中国海域\n        [118.1, 24.5], // 厦门\n        [121.5, 31.2], // 上海\n        [113.3, 23.1]  // 广州\n      ],\n      currentCenterIndex: 0\n    }\n  },\n  methods: {\n    onMapReady(map) {\n      this.mapStatus = '地图加载成功'\n      console.log('地图初始化完成:', map)\n    },\n    \n    onMapError(error) {\n      this.mapStatus = '地图加载失败: ' + error.message\n      console.error('地图初始化失败:', error)\n    },\n    \n    onMapClick(event) {\n      if (event.latlng) {\n        this.clickPosition = `${event.latlng.lat.toFixed(4)}, ${event.latlng.lng.toFixed(4)}`\n      }\n      console.log('地图点击事件:', event)\n    },\n    \n    onLayerChanged(layerKey) {\n      this.currentLayer = layerKey\n      console.log('图层切换:', layerKey)\n    },\n    \n    changeCenter() {\n      this.currentCenterIndex = (this.currentCenterIndex + 1) % this.testCenters.length\n      this.mapCenter = [...this.testCenters[this.currentCenterIndex]]\n      \n      // 使用组件方法设置中心点\n      if (this.$refs.bigMap) {\n        this.$refs.bigMap.setCenter(this.mapCenter[0], this.mapCenter[1], this.mapZoom)\n      }\n    },\n    \n    addTestMarker() {\n      if (this.$refs.bigMap) {\n        const randomLng = this.mapCenter[0] + (Math.random() - 0.5) * 2\n        const randomLat = this.mapCenter[1] + (Math.random() - 0.5) * 2\n        \n        const marker = this.$refs.bigMap.addMarker(randomLng, randomLat, {\n          title: `标记 ${this.markers.length + 1}`\n        })\n        \n        if (marker) {\n          this.markers.push(marker)\n          this.$message.success(`添加标记成功，位置: ${randomLng.toFixed(4)}, ${randomLat.toFixed(4)}`)\n        }\n      }\n    },\n    \n    clearMarkers() {\n      if (this.$refs.bigMap) {\n        this.markers.forEach(marker => {\n          this.$refs.bigMap.removeMarker(marker)\n        })\n        this.markers = []\n        this.$message.success('已清除所有标记')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.map-test-container {\n  padding: 20px;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: #f5f5f5;\n  border-radius: 4px;\n}\n\n.header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.controls {\n  display: flex;\n  gap: 10px;\n}\n\n.map-container {\n  flex: 1;\n  border: 2px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.info-panel {\n  margin-top: 20px;\n  padding: 15px;\n  background: #f9f9f9;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\n.info-panel h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.info-panel p {\n  margin: 8px 0;\n  color: #666;\n}\n</style>\n"]}]}